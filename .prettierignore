# Dependencies
node_modules/

# Build output
dist/
build/
coverage/
*.log

# Generated files
dist-manifest.json
dist-manifest.yaml
*.min.js
lcov.info

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/
operations/test-temp/

# Documentation HTML
docs/**/*.html
ai-docs/**/*.html

# Ignore problematic YAML files
ai-docs/*.yaml
ai-docs/*.yml

# Logs
logs/
CHANGELOG.md

# Workspaces (agent-generated content)
workspaces/

# Git
.git/

# Environment
.env
.env.local
.env.*.local

# Claude Code configuration
.claude/commands/

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Test fixtures
test/fixtures/

# Markdown files (optional - remove if you want to format markdown)
**/ *.md

# JSON files that shouldn't be formatted
shared/deployment-plans/*.json
shared/coordination/*.json
validation/*.json