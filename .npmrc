# NPM Configuration for Claude Parallel Development Package
# This file configures npm settings for publishing and installation

# Registry configuration
registry=https://registry.npmjs.org/

# Publishing configuration
access=public

# Security settings
audit-level=moderate
fund=false

# Performance settings
prefer-online=true
fetch-retry-mintimeout=10000
fetch-retry-maxtimeout=60000
fetch-retry-factor=10
fetch-retries=5

# Package scope settings (if using scoped packages)
# @your-org:registry=https://registry.npmjs.org/

# Engine strict mode
engine-strict=true

# Save exact versions for dependencies
save-exact=true

# Logging level
loglevel=warn

# Optional: Git settings for npm version
git-tag-version=true
tag-version-prefix=v

# Optional: Sign git commits and tags
sign-git-commit=false
sign-git-tag=false