# ==================================================================
# CDEV (Claude Development) - Environment Configuration
# ==================================================================
# Copy this file to .env and update with your actual API keys
# NEVER commit .env files to version control

# ==================================================================
# RECOMMENDED CONFIGURATION FOR OPTIMAL PERFORMANCE
# ==================================================================

# Linear Integration (Required for issue management)
# Get your API key from: https://linear.app/settings/api
LINEAR_API_KEY=<your-linear-api-key>

# LLM Provider Configuration
# Choose one provider and configure accordingly
LLM_PROVIDER=openrouter

# ==================================================================
# RECOMMENDED MODEL FOR TASK DECOMPOSITION
# ==================================================================
# Based on testing, Mistral Small performs well for semantic analysis
# and intelligent parallel task decomposition
LLM_MODEL=mistralai/mistral-small-3.2-24b-instruct:free
# Alternative: google/gemini-2.5-pro (also excellent for decomposition)

# ==================================================================
# API KEYS (Configure based on your chosen provider)
# ==================================================================

# OpenRouter (Recommended - provides access to multiple models)
OPENROUTER_API_KEY=sk-or-your-openrouter-key-here

# Direct Provider Keys (if not using OpenRouter)
OPENAI_API_KEY=sk-your-openai-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
GOOGLE_API_KEY=your-google-api-key-here

# ==================================================================
# PROJECT CONFIGURATION
# ==================================================================

# Project name for generated workflows
PROJECT_NAME=my-project

# Worktree base path (where parallel agent directories are created)
WORKTREE_BASE_PATH=../my-project-work-trees

# Engineer name for personalized notifications
ENGINEER_NAME=YourName

# ==================================================================
# ADVANCED CONFIGURATION
# ==================================================================

# Verbose logging for debugging
DEBUG=false

# Temperature for LLM responses (0.0-1.0, lower = more deterministic)
LLM_TEMPERATURE=0.2

# Maximum tokens for LLM responses
LLM_MAX_TOKENS=4000

# Timeout for LLM requests (milliseconds)
LLM_TIMEOUT=30000

# ==================================================================
# OPTIONAL INTEGRATIONS
# ==================================================================

# Text-to-Speech notifications (optional)
OPENAI_API_KEY=your-openai-key-for-tts
ELEVENLABS_API_KEY=your-elevenlabs-key-here

# Additional AI providers (optional)
DEEPSEEK_API_KEY=your-deepseek-key-here
GROQ_API_KEY=your-groq-key-here

# Local LLM server (optional)
OLLAMA_HOST=http://localhost:11434

# ==================================================================
# SECURITY NOTES
# ==================================================================
# 1. Never share or commit API keys
# 2. Use environment-specific .env files
# 3. Consider using a secrets manager in production
# 4. Rotate API keys regularly
# 5. Monitor API usage and set billing alerts