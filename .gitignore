# Environment variables
.env
.env.local
.env.*.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Production build files
dist/
build/
dist-manifest.json
dist-manifest.yaml

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Cache
.cache/
.linear-cache/
.ruff_cache/

# Shared development artifacts
/shared/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/
archive/

# Package artifacts
*.tgz
cdev@*

# Test outputs
test-output/

# IDE specific
.cursor/

# Git worktrees coordination
../project-work-trees/
workspaces/
coordination/node_modules/
.cache/
node_modules/
*.cpython-*.pyc
__pycache__/
coverage/
please-delete/

# Development test files (temporary)
test-hook-installer.js
test-hook-mapping.js
test-installation/
test-mapping/
archive/test-hook-installer.js
archive/test-hook-mapping.js
# Parallel development workspace files
agent_context_*.yaml
validation_checklist_*.txt
task-deployment-plan.yaml
