name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  test:
    name: Test on Node ${{ matrix.node }} and ${{ matrix.os }}
    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        node: [16, 18, 20]
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            python: '3.9'
          - os: windows-latest
            python: '3.9'
          - os: macos-latest
            python: '3.11'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: 'npm'

      - name: Setup Python ${{ matrix.python }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python }}

      - name: Install UV Package Manager
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Get npm cache directory
        id: npm-cache-dir
        shell: bash
        run: echo "dir=$(npm config get cache)" >> ${GITHUB_OUTPUT}

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ${{ steps.npm-cache-dir.outputs.dir }}
          key: ${{ runner.os }}-node-${{ matrix.node }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ matrix.node }}-
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Make Python scripts executable
        run: chmod +x scripts/python/*.py

      - name: Run JavaScript linter
        run: npm run lint
        continue-on-error: true

      - name: Install Ruff
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          export PATH="$HOME/.cargo/bin:$PATH"
          uv tool install ruff

      - name: Run Python linter
        run: |
          export PATH="$HOME/.local/bin:$PATH"
          ruff check scripts/python/ --output-format=github
        continue-on-error: true

      - name: Run unit tests
        run: npm run test:unit
        env:
          CI: true

      - name: Run integration tests
        run: npm run test:integration
        env:
          CI: true
          LINEAR_API_KEY: ${{ secrets.LINEAR_API_KEY }}

      - name: Generate coverage report
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-${{ matrix.os }}-node-${{ matrix.node }}
          fail_ci_if_error: false

  check-package:
    name: Check Package
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Check package size
        run: |
          npm pack --dry-run
          size=$(npm pack --dry-run 2>&1 | grep "npm notice" | grep -E "[0-9]+(\.[0-9]+)? [kMG]B" | awk '{print $(NF-1), $NF}')
          echo "Package size: $size"

          # Convert to bytes for comparison
          size_bytes=$(echo $size | awk '{
            num = $1
            unit = $2
            if (unit == "kB") num *= 1024
            else if (unit == "MB") num *= 1024 * 1024
            else if (unit == "GB") num *= 1024 * 1024 * 1024
            print int(num)
          }')

          # Check if under 10MB
          if [ $size_bytes -gt 10485760 ]; then
            echo "Package size exceeds 10MB limit!"
            exit 1
          fi

      - name: Test package installation
        run: |
          # Pack the package
          npm pack

          # Create test directory
          mkdir test-install
          cd test-install

          # Initialize test project
          npm init -y

          # Install the package
          npm install ../claude-code-hooks-*.tgz

          # Test CLI availability
          npx claude-code-hooks --version

  security:
    name: Security Audit
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install UV Package Manager
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Install dependencies
        run: npm ci

      - name: Make Python scripts executable
        run: chmod +x scripts/python/*.py

      - name: Run Python security check
        run: ./scripts/python/security-check.py
        env:
          CI: true

      - name: Run npm security audit
        run: npm audit --production
        continue-on-error: true

      - name: Check for known vulnerabilities
        run: npx audit-ci --moderate

  quality:
    name: Code Quality
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: npm ci

      - name: Install Ruff
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          export PATH="$HOME/.cargo/bin:$PATH"
          uv tool install ruff

      - name: Check JavaScript formatting
        run: npx prettier --check "**/*.{js,json,md}"
        continue-on-error: true

      - name: Check Python formatting
        run: |
          export PATH="$HOME/.local/bin:$PATH"
          ruff format scripts/python/ --check
        continue-on-error: true

      - name: Run Python linting
        run: |
          export PATH="$HOME/.local/bin:$PATH"
          ruff check scripts/python/ --output-format=github
        continue-on-error: true

      - name: Type check
        run: npx tsc --noEmit
        continue-on-error: true

      - name: Check for unused dependencies
        run: npx depcheck
        continue-on-error: true

  local-test:
    name: Local Test Validation
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install UV Package Manager
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Install dependencies
        run: npm ci

      - name: Make Python scripts executable
        run: chmod +x scripts/python/*.py

      - name: Run local test validation
        run: ./scripts/python/test-locally.py
        env:
          CI: true
          TEST_MODE: validation
