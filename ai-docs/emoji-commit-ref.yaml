# A comprehensive list of conventional commit types with corresponding emojis.
# This can be used to enforce or generate standardized commit messages.
commit_message_conventions:
  - emoji: '✨'
    type: 'feat'
    description: 'New feature'
  - emoji: '🐛'
    type: 'fix'
    description: 'Bug fix'
  - emoji: '📝'
    type: 'docs'
    description: 'Documentation'
  - emoji: '💄'
    type: 'style'
    description: 'Formatting/style'
  - emoji: '♻️'
    type: 'refactor'
    description: 'Code refactoring'
  - emoji: '⚡️'
    type: 'perf'
    description: 'Performance improvements'
  - emoji: '✅'
    type: 'test'
    description: 'Tests'
  - emoji: '🔧'
    type: 'chore'
    description: 'Tooling, configuration'
  - emoji: '🚀'
    type: 'ci'
    description: 'CI/CD improvements'
  - emoji: '🗑️'
    type: 'revert'
    description: 'Reverting changes'
  - emoji: '🧪'
    type: 'test'
    description: 'Add a failing test'
  - emoji: '🚨'
    type: 'fix'
    description: 'Fix compiler/linter warnings'
  - emoji: '🔒️'
    type: 'fix'
    description: 'Fix security issues'
  - emoji: '👥'
    type: 'chore'
    description: 'Add or update contributors'
  - emoji: '🚚'
    type: 'refactor'
    description: 'Move or rename resources'
  - emoji: '🏗️'
    type: 'refactor'
    description: 'Make architectural changes'
  - emoji: '🔀'
    type: 'chore'
    description: 'Merge branches'
  - emoji: '📦️'
    type: 'chore'
    description: 'Add or update compiled files or packages'
  - emoji: '➕'
    type: 'chore'
    description: 'Add a dependency'
  - emoji: '➖'
    type: 'chore'
    description: 'Remove a dependency'
  - emoji: '🌱'
    type: 'chore'
    description: 'Add or update seed files'
  - emoji: '🧑‍💻'
    type: 'chore'
    description: 'Improve developer experience'
  - emoji: '🧵'
    type: 'feat'
    description: 'Add or update code related to multithreading or concurrency'
  - emoji: '🔍️'
    type: 'feat'
    description: 'Improve SEO'
  - emoji: '🏷️'
    type: 'feat'
    description: 'Add or update types'
  - emoji: '💬'
    type: 'feat'
    description: 'Add or update text and literals'
  - emoji: '🌐'
    type: 'feat'
    description: 'Internationalization and localization'
  - emoji: '👔'
    type: 'feat'
    description: 'Add or update business logic'
  - emoji: '📱'
    type: 'feat'
    description: 'Work on responsive design'
  - emoji: '🚸'
    type: 'feat'
    description: 'Improve user experience / usability'
  - emoji: '🩹'
    type: 'fix'
    description: 'Simple fix for a non-critical issue'
  - emoji: '🥅'
    type: 'fix'
    description: 'Catch errors'
  - emoji: '👽️'
    type: 'fix'
    description: 'Update code due to external API changes'
  - emoji: '🔥'
    type: 'fix'
    description: 'Remove code or files'
  - emoji: '🎨'
    type: 'style'
    description: 'Improve structure/format of the code'
  - emoji: '🚑️'
    type: 'fix'
    description: 'Critical hotfix'
  - emoji: '🎉'
    type: 'chore'
    description: 'Begin a project'
  - emoji: '🔖'
    type: 'chore'
    description: 'Release/Version tags'
  - emoji: '🚧'
    type: 'wip'
    description: 'Work in progress'
  - emoji: '💚'
    type: 'fix'
    description: 'Fix CI build'
  - emoji: '📌'
    type: 'chore'
    description: 'Pin dependencies to specific versions'
  - emoji: '👷'
    type: 'ci'
    description: 'Add or update CI build system'
  - emoji: '📈'
    type: 'feat'
    description: 'Add or update analytics or tracking code'
  - emoji: '✏️'
    type: 'fix'
    description: 'Fix typos'
  - emoji: '⏪️'
    type: 'revert'
    description: 'Revert changes'
  - emoji: '📄'
    type: 'chore'
    description: 'Add or update license'
  - emoji: '💥'
    type: 'feat'
    description: 'Introduce breaking changes'
  - emoji: '🍱'
    type: 'assets'
    description: 'Add or update assets'
  - emoji: '♿️'
    type: 'feat'
    description: 'Improve accessibility'
  - emoji: '💡'
    type: 'docs'
    description: 'Add or update comments in source code'
  - emoji: '🗃️'
    type: 'db'
    description: 'Perform database related changes'
  - emoji: '🔊'
    type: 'feat'
    description: 'Add or update logs'
  - emoji: '🔇'
    type: 'fix'
    description: 'Remove logs'
  - emoji: '🤡'
    type: 'test'
    description: 'Mock things'
  - emoji: '🥚'
    type: 'feat'
    description: 'Add or update an easter egg'
  - emoji: '🙈'
    type: 'chore'
    description: 'Add or update .gitignore file'
  - emoji: '📸'
    type: 'test'
    description: 'Add or update snapshots'
  - emoji: '⚗️'
    type: 'experiment'
    description: 'Perform experiments'
  - emoji: '🚩'
    type: 'feat'
    description: 'Add, update, or remove feature flags'
  - emoji: '💫'
    type: 'ui'
    description: 'Add or update animations and transitions'
  - emoji: '⚰️'
    type: 'refactor'
    description: 'Remove dead code'
  - emoji: '🦺'
    type: 'feat'
    description: 'Add or update code related to validation'
  - emoji: '✈️'
    type: 'feat'
    description: 'Improve offline support'

# Issue linking and auto-close magic words for GitHub and Linear integration
issue_linking_conventions:
  closing_keywords:
    description: 'Keywords that automatically close issues when PR is merged'
    keywords:
      - 'close'
      - 'closes'
      - 'closed'
      - 'fix'
      - 'fixes'
      - 'fixed'
      - 'resolve'
      - 'resolves'
      - 'resolved'
      - 'complete'
      - 'completes'
      - 'completed'
      - 'closing'
      - 'fixing'
      - 'resolving'
      - 'completing'
  
  referencing_keywords:
    description: 'Keywords that link to issues without closing them'
    keywords:
      - 'ref'
      - 'references'
      - 'part of'
      - 'related to'
      - 'contributes to'
      - 'towards'
  
  format_examples:
    github_issues:
      - pattern: '#<issue-number>'
        example: 'Closes #42'
        description: 'References issue in the current repository'
      
      - pattern: '<org>/<repo>#<issue-number>'
        example: 'Fixes user-org/repo#456'
        description: 'References issue in another repository'
    
    linear_issues:
      - pattern: '<team-key>-<issue-number>'
        example: 'Fixes ENG-123'
        description: 'References Linear issue using team key format'
      
      - pattern: 'Multiple issues'
        example: 'Fixes ENG-123, ENG-124'
        description: 'Can reference multiple issues with comma separation'
  
  commit_message_examples:
    - |
      ✨ feat: add user authentication
      
      Implements JWT-based authentication with refresh tokens.
      
      Closes #42
      Fixes ENG-123, ENG-124
    
    - |
      🐛 fix: resolve login validation error
      
      Properly validates email format before submission.
      
      Resolves user-org/api#789
    
    - |
      ♻️ refactor: improve database query performance
      
      Optimizes user lookup queries with proper indexing.
      
      Part of #100
      Related to ENG-456
  
  best_practices:
    - 'Place issue references at the end of the commit body, not in the subject line'
    - 'Use closing keywords when the commit fully addresses the issue'
    - 'Use referencing keywords when the commit partially addresses the issue'
    - 'For Linear issues, ensure your team key is correct (e.g., ENG, CDE, etc.)'
    - 'Always verify issue numbers before committing to avoid linking to wrong issues'
