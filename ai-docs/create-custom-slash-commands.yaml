# A guide for creating and using custom slash commands in Claude Code.
slash_command_creation_guide:
  # Commands that are specific to a project and shared with the team.
  project_specific_commands:
    description: 'Create reusable slash commands for your project that all team members can use.'
    steps:
      - title: 'Create a commands directory in your project'
        command: 'mkdir -p .claude/commands'
      - title: 'Create a Markdown file for each command'
        command: 'echo "Analyze the performance of this code and suggest three specific optimizations:" > .claude/commands/optimize.md'
      - title: 'Use your custom command in Claude Code'
        usage: '/optimize'
    tips:
      - 'Command names are derived from the filename (e.g., `optimize.md` becomes `/optimize`).'
      - "You can organize commands in subdirectories (e.g., `.claude/commands/frontend/component.md` creates `/component` with a '(project:frontend)' description)."
      - 'Project commands are available to everyone who clones the repository.'
      - 'The Markdown file content becomes the prompt sent to <PERSON> when the command is invoked.'

  # Flexible commands that can accept additional user input.
  commands_with_arguments:
    description: 'Create flexible slash commands that can accept additional input from users by using the $ARGUMENTS placeholder.'
    steps:
      - title: 'Create a command file with the $ARGUMENTS placeholder'
        command: |
          echo "Find and fix issue #$ARGUMENTS. Follow these steps: 1.
          Understand the issue described in the ticket 2. Locate the relevant code in
          our codebase 3. Implement a solution that addresses the root cause 4. Add
          appropriate tests 5. Prepare a concise PR description" >
          .claude/commands/fix-issue.md
      - title: 'Use the command with an issue number'
        description: "In your Claude session, use the command with arguments. This will replace $ARGUMENTS with '123' in the prompt."
        usage: '/fix-issue 123'
    tips:
      - 'The `$ARGUMENTS` placeholder is replaced with any text that follows the command.'
      - 'You can position `$ARGUMENTS` anywhere in your command template.'
      - 'Useful applications include generating test cases for specific functions, creating documentation for components, or reviewing code in particular files.'

  # Commands that are personal to you and work across all your projects.
  personal_commands:
    description: 'Create personal slash commands that work across all your projects and are not shared with your team.'
    steps:
      - title: 'Create a commands directory in your home folder'
        command: 'mkdir -p ~/.claude/commands'
      - title: 'Create a Markdown file for each command'
        command: 'echo "Review this code for security vulnerabilities, focusing on:" > ~/.claude/commands/security-review.md'
      - title: 'Use your personal custom command'
        usage: '/security-review'
    tips:
      - "Personal commands show '(user)' in their description when listed with `/help`."
      - 'Personal commands are only available to you and are not shared with your team.'
      - 'Personal commands work across all your projects, allowing for consistent workflows.'
