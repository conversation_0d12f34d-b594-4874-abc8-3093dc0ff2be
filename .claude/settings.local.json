{"permissions": {"allow": ["Read", "Write", "Edit", "MultiEdit", "Grep", "Glob", "LS", "NotebookRead", "WebFetch", "WebSearch", "Bash(npm install:*)", "Bash(npm run:*)", "Bash(npm ci:*)", "Bash(npm list:*)", "Bash(npm info:*)", "Bash(npm audit:*)", "Bash(npm outdated:*)", "Bash(pnpm install:*)", "Bash(pnpm add:*)", "Bash(pnpm remove:*)", "Bash(pnpm run:*)", "Bash(pnpm list:*)", "Bash(pnpm why:*)", "Bash(pnpm outdated:*)", "Bash(uv install:*)", "<PERSON><PERSON>(uv run:*)", "Bash(uv sync:*)", "<PERSON><PERSON>(uv list:*)", "Bash(yarn install:*)", "Bash(yarn add:*)", "Bash(yarn remove:*)", "Bash(yarn run:*)", "Bash(git status:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git branch:*)", "Bash(git checkout:*)", "Bash(git log:*)", "Bash(git diff:*)", "Bash(git stash:*)", "Bash(git fetch:*)", "<PERSON><PERSON>(git worktree:*)", "<PERSON><PERSON>(tsc:*)", "Bash(eslint:*)", "<PERSON><PERSON>(prettier:*)", "<PERSON><PERSON>(jest:*)", "Bash(vitest:*)", "Bash(webpack:*)", "Bash(vite:*)", "Bash(ls:*)", "Bash(pwd)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "Bash(wc:*)", "Bash(which:*)", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(touch:*)", "Bash(ps:*)", "<PERSON><PERSON>(jobs:*)", "Bash(env:*)", "<PERSON><PERSON>(printenv:*)", "<PERSON><PERSON>(history:*)", "Bash(node ./scripts/decompose-parallel.cjs:*)", "Bash(node ./scripts/cache-linear-issue.sh:*)", "Bash(./spawn-agents.sh:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(python3:*)", "Ba<PERSON>(pip list:*)", "<PERSON><PERSON>(pip show:*)", "Bash(gh repo view:*)", "Bash(gh issue list:*)", "Bash(gh pr list:*)", "Bash(bun run:*)", "<PERSON><PERSON>(deno run:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(true)", "Bash(node:*)", "Bash(npm test:*)", "Bash(NODE_OPTIONS='--max-old-space-size=4096' npx jest --coverage --watchAll=false --passWithNoTests --reporters=default --coverageReporters=text-summary)", "Bash(CI=true NODE_ENV=test npx jest test/template-engine.test.js --verbose --no-coverage)", "Bash(CI=true NODE_ENV=test npx jest src/validation-reporter.test.js --verbose --no-coverage)", "Bash(CI=true NODE_ENV=test npx jest --testMatch=\"**/src/{config-generator,python-detector,config-migrator,platform-utils,hook-manager,path-resolver,hook-categorizer,hook-organizer,config-validator,hook-selector,intelligent-agent-generator}.test.js\" --verbose --no-coverage)", "Bash(CI=true NODE_ENV=test npx jest src/config-generator.test.js --verbose --no-coverage)", "Bash(CI=true NODE_ENV=test npm test -- --maxWorkers=1 --bail=false --testTimeout=10000)", "Bash(CI=true NODE_ENV=test npx jest --listTests)", "Bash(npm view:*)", "Bash(eza:*)", "Bash(scripts/python/cache-linear-issue.py:*)", "<PERSON><PERSON>(scripts/python/security-check.py:*)", "Bash(scripts/python/decompose-parallel.py:*)", "<PERSON><PERSON>(scripts/*)", "Bash(scripts/python/spawn-agents.py:*)", "<PERSON><PERSON>(scripts/python/monitor-agents.py:*)", "Bash(cdev:*)", "<PERSON><PERSON>(sed:*)", "Bash(ALLOW_DIRTY=1 npm pack --dry-run)", "Bash(tar:*)", "<PERSON><PERSON>(realpath:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(ALLOW_DIRTY=1 npm publish)", "mcp__exa__web_search_exa"], "deny": []}}