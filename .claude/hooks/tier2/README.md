# Tier 2 - Important Hooks

This directory contains important quality and standards hooks that improve code quality and maintainability.

## Hooks in this tier:

- **api-standards-checker.py**: Checks API code against standards
- **code-quality-reporter.py**: Reports on code quality metrics
- **universal-linter.py**: Runs linting across multiple file types
- **import-organizer.py**: Organizes and sorts import statements

## Characteristics:

- Quality-focused
- Standards enforcement
- Recommended for most projects
- Can be selectively disabled

## Usage:

These hooks are recommended for all projects but can be excluded based on project needs.
