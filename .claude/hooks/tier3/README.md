# Tier 3 - Optional Hooks

This directory contains optional convenience and notification hooks that provide additional functionality.

## Hooks in this tier:

- **commit-message-validator.py**: Validates commit message format and content
- **typescript-validator.py**: Validates TypeScript code and type safety
- **task-completion-enforcer.py**: Ensures tasks are completed before proceeding
- **pnpm-enforcer.py**: Enforces use of pnpm package manager

## Characteristics:

- Convenience features
- Optional enhancements
- Project-specific utilities
- Can be freely enabled/disabled

## Usage:

These hooks are optional and can be selectively enabled based on project requirements and developer preferences.
