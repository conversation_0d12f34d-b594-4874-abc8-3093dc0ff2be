---
allowed-tools: Read, mcp__linear__create_issue, mcp__linear__get_project, mcp__linear__get_team, mcp__linear__get_user, mcp__linear__list_issue_labels, mcp__linear__list_issue_statuses, mcp__linear__list_projects, mcp__linear__list_teams, mcp__linear__list_users, mcp__linear__update_issue
description: Create well-structured Linear issues for parallel development workflow
---

# Write Linear Issue

Create well-structured Linear issues optimized for parallel development workflow using Linear MCP tools. Use $ARGUMENTS for feature description and team identifier, fetch team and project context via mcp__linear__list_teams and related tools, structure issue with numbered tasks, acceptance criteria, and technical constraints following ai-docs/linear-issue-template.yaml format, then create issue via mcp__linear__create_issue and provide issue ID and URL.
