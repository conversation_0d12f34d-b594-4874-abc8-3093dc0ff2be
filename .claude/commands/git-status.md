---
allowed-tools: Bash, Read
description: Analyze current git repository state and differences from remote
---

# Git Status

Analyze current git repository state including status, branch information, differences from remote, and recent commits. Use $ARGUMENTS for specific branch or filter options, provide actionable summary with next steps recommendations highlighting any uncommitted changes or divergence from remote branch.
