---
allowed-tools: Ba<PERSON>, Read, Glob, Grep
description: Validate parallel agent work completion with 100% verification
---

# Agent Final Validation

Use the quality-guardian sub-agent to perform strict 100% validation of parallel agent work for $ARGUMENTS. Analyze git history, verify file commits, check validation criteria completion, and generate comprehensive pass/fail report with remediation steps for any failures.
