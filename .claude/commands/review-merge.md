---
allowed-tools: Bash, Edit, Grep, MultiEdit, Read, TodoWrite, WebFetch, Write
description: Review and merge pull requests with comprehensive validation and safety checks
---

# Review Merge

Review and merge pull requests with comprehensive validation and safety checks. Parse $ARGUMENTS for PR number and merge strategy (merge/squash/rebase), fetch PR details via gh commands, validate CI checks and reviews, verify test coverage and security scans, perform interactive review of changes, execute merge with selected strategy, and handle post-merge cleanup including branch deletion and Linear task updates.
