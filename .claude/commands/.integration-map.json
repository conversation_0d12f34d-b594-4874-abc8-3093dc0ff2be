{"command_ecosystem": {"workflow_chains": {"parallel_development": ["orchestrate", "agent-start", "agent-status", "agent-final-validation", "agent-commit", "agent-cleanup"], "pull_request_flow": ["commit", "create-pr", "pr-review", "review-merge"], "project_setup": ["prime", "init-protocol", "enforce-structure", "generate-readme"], "maintenance": ["git-status", "update-changelog", "deep-search", "all-tools"]}, "sub_agent_mapping": {"git-flow-manager": ["agent-commit", "commit", "review-merge"], "agent-coordinator": ["agent-start"], "task-orchestrator": ["orchestrate", "build-roadmap"], "quality-guardian": ["agent-final-validation"], "pr-specialist": ["create-pr"], "structure-enforcer": ["enforce-structure"], "deep-searcher": ["deep-search", "init-protocol"], "doc-curator": ["generate-readme"], "roadmap-architect": ["build-roadmap"]}, "integration_points": {"mcp_tools": ["write-linear-issue", "pr-review"], "file_operations": ["create-coordination-files", "rule2hook", "create-command"], "analysis_tools": ["quick-search", "all-tools", "git-status", "prime"]}}}