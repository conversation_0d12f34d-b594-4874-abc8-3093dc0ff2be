---
allowed-tools: Bash, Read, Write, Edit
description: Generate coordination files for parallel workflow integration
---

# Create Coordination Files

Generate coordination files for parallel workflow integration in agent workspace $ARGUMENTS. Read agent_context.yaml and validation_checklist.txt, calculate completion percentage, create status files and deployment plans in shared/coordination/ directory for seamless workflow integration.
