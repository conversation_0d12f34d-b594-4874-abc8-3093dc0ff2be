---
allowed-tools: <PERSON><PERSON>, <PERSON>za, Git, <PERSON><PERSON>b, <PERSON><PERSON><PERSON>, Read, Write
description: Generate comprehensive README using structured template with project analysis
---

# Generate README

Use the doc-curator sub-agent to generate comprehensive README files using structured template with project analysis. Parse $ARGUMENTS for target file path and template options, analyze project structure via eza commands and git history, extract metadata from configuration files (package.json, setup.py), load ai-docs/readme-template.yaml template, apply <PERSON><PERSON><PERSON> technique for clear explanations, substitute template variables with project-specific content, generate navigation and setup instructions, and write final README.md with summary of sections created.
