---
allowed-tools: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Read, Task, Write
description: Initialize protocol-based CLAUDE.md with intelligent framework selection
---

# Init Protocol

Use the deep-searcher sub-agent to generate comprehensive protocol-based CLAUDE.md file with intelligent framework selection. Check existing CLAUDE.md with $ARGUMENTS (--force to overwrite), analyze project structure and documentation to understand actual project goals (not just framework), determine complexity score and project type, select appropriate protocol categories, load ai-docs/CLAUDE-protocol-template.yaml, customize protocols for project context, include sub-agent usage patterns and CDEV integration guidance, and save final CLAUDE.md with project-specific quick start guide.
