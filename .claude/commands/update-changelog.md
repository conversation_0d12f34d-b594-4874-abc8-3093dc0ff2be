---
allowed-tools: Bash, Read, Edit
description: Update project CHANGELOG.md automatically from git commits
---

# Update Changelog

Run `npm run changelog:force` to automatically update CHANGELOG.md from git commits. Review generated entry for $ARGUMENTS version, edit to follow conventions in ai-docs/changelog-conventions.md (proper categorization, clear descriptions, technical context), and commit the updated CHANGELOG.md file.