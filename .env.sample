# LLM Configuration for Parallel Development Workflow

# LLM Provider (openai, anthropic, openrouter, ollama)
LLM_PROVIDER=openrouter

# API Keys (set one based on your provider)
OPENAI_API_KEY=sk-your-openai-key-here
ANTHROPIC_API_KEY=your-anthropic-key-here
OPENROUTER_API_KEY=sk-or-your-openrouter-key-here

# Model Configuration
#LLM_MODEL=openai/gpt-4.1-nano-2025-04-14
#LLM_MODEL=google/gemini-2.5-pro
#LLM_MODEL=google/gemini-2.5-flash
#LLM_MODEL=anthropic/claude-sonnet-4
#LLM_MODEL=openai/gpt-4.1-mini
#LLM_MODEL=deepseek/deepseek-chat-v3-0324
#LLM_MODEL=qwen/qwen-2.5-coder-32b-instruct
#LLM_MODEL=openai/o3
#LLM_MODEL=openai/o3-mini-high
#LLM_MODEL=openai/o4-mini-high
#LLM_MODEL=x-ai/grok-3
#LLM_MODEL=tencent/hunyuan-a13b-instruct:free
#LLM_MODEL=mistralai/mistral-small-3.2-24b-instruct:free
#LLM_MODEL=mistralai/devstral-small:free
# For OpenAI: gpt-4, gpt-3.5-turbo, gpt-4o-mini
# For Anthropic: claude-3-sonnet-20240229, claude-3-haiku-20240307
# For OpenRouter: openai/gpt-4.1-nano-2025-04-14, google/gemini-pro, anthropic/claude-3-sonnet
# For Ollama: llama2, codellama, mistral

# TTS Configuration (if needed)
TTS_MODEL=gpt-4o-mini-tts
# For OpenAI TTS: tts-1, tts-1-hd, gpt-4o-mini-tts

# Linear Integration (optional)
LINEAR_API_KEY=lin_api_your-linear-key-here