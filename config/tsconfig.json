{"compilerOptions": {"target": "es2017", "module": "commonjs", "lib": ["es2017", "dom", "dom.iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx"}, "include": ["src/**/*", "lib/**/*", "components/**/*", "hooks/**/*", "utils/**/*", "api/**/*", "types/**/*", "tests/**/*", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "exclude": ["node_modules", "coverage", "logs", "shared", "workspaces", "ai_docs", "templates"]}